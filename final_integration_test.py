#!/usr/bin/env python3
"""
MCP Feedback Enhanced 最终集成测试
验证完整的工作流程
"""
import os
import sys
import json
import subprocess
import time
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🎯 {title}")
    print("=" * 60)

def print_step(step, description):
    """打印步骤"""
    print(f"\n📋 步骤 {step}: {description}")
    print("-" * 40)

def check_installation():
    """检查安装状态"""
    print_step(1, "检查安装状态")
    
    checks = []
    
    # 检查Python版本
    try:
        result = subprocess.run(['python3.11', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Python: {version}")
            checks.append(True)
        else:
            print("❌ Python 3.11 未安装")
            checks.append(False)
    except FileNotFoundError:
        print("❌ Python 3.11 未找到")
        checks.append(False)
    
    # 检查uv
    try:
        result = subprocess.run(['uv', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ UV: {version}")
            checks.append(True)
        else:
            print("❌ UV 未安装")
            checks.append(False)
    except FileNotFoundError:
        print("❌ UV 未找到")
        checks.append(False)
    
    # 检查MCP Feedback Enhanced
    try:
        result = subprocess.run(['uvx', 'mcp-feedback-enhanced@latest', 'version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ MCP Feedback Enhanced: 已安装")
            checks.append(True)
        else:
            print("❌ MCP Feedback Enhanced 安装失败")
            checks.append(False)
    except Exception as e:
        print(f"❌ MCP Feedback Enhanced 检查失败: {e}")
        checks.append(False)
    
    return all(checks)

def check_configuration():
    """检查配置文件"""
    print_step(2, "检查配置文件")
    
    config_file = Path("mcp-config.json")
    
    if not config_file.exists():
        print("❌ mcp-config.json 不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查MCP Feedback Enhanced配置
        if 'mcpServers' in config and 'mcp-feedback-enhanced' in config['mcpServers']:
            mcp_config = config['mcpServers']['mcp-feedback-enhanced']
            
            print("✅ MCP配置文件存在")
            print(f"   命令: {mcp_config.get('command', 'N/A')}")
            print(f"   参数: {mcp_config.get('args', 'N/A')}")
            
            env_config = mcp_config.get('env', {})
            print(f"   主机: {env_config.get('MCP_WEB_HOST', 'N/A')}")
            print(f"   端口: {env_config.get('MCP_WEB_PORT', 'N/A')}")
            print(f"   语言: {env_config.get('MCP_LANGUAGE', 'N/A')}")
            
            return True
        else:
            print("❌ MCP Feedback Enhanced 配置缺失")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件读取失败: {e}")
        return False

def test_mcp_server():
    """测试MCP服务器"""
    print_step(3, "测试MCP服务器")
    
    env = os.environ.copy()
    env.update({
        'MCP_WEB_HOST': '0.0.0.0',
        'MCP_WEB_PORT': '8765',
        'MCP_LANGUAGE': 'zh-CN',
        'MCP_DEBUG': 'false'
    })
    
    try:
        print("🚀 启动MCP服务器...")
        process = subprocess.Popen(
            ['uvx', 'mcp-feedback-enhanced@latest', 'server'],
            env=env,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        # 等待启动
        time.sleep(3)
        
        # 发送初始化消息
        init_message = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {"name": "test-client", "version": "1.0.0"}
            }
        }
        
        process.stdin.write(json.dumps(init_message) + '\n')
        process.stdin.flush()
        
        # 读取响应
        response = process.stdout.readline()
        
        # 清理
        process.terminate()
        process.wait()
        
        if response and '"result"' in response:
            print("✅ MCP服务器响应正常")
            return True
        else:
            print("❌ MCP服务器响应异常")
            return False
            
    except Exception as e:
        print(f"❌ MCP服务器测试失败: {e}")
        return False

def generate_vscode_config():
    """生成VSCode配置"""
    print_step(4, "生成VSCode配置")
    
    vscode_config = {
        "mcpServers": {
            "mcp-feedback-enhanced": {
                "command": "uvx",
                "args": ["mcp-feedback-enhanced@latest"],
                "timeout": 600,
                "env": {
                    "MCP_WEB_HOST": "0.0.0.0",
                    "MCP_WEB_PORT": "8765",
                    "MCP_DEBUG": "false",
                    "MCP_LANGUAGE": "zh-CN"
                },
                "autoApprove": ["interactive_feedback"]
            }
        }
    }
    
    try:
        with open('vscode_mcp_config.json', 'w', encoding='utf-8') as f:
            json.dump(vscode_config, f, indent=2, ensure_ascii=False)
        
        print("✅ VSCode配置已生成: vscode_mcp_config.json")
        print("\n📋 请将以下配置添加到VSCode的settings.json中:")
        print("-" * 50)
        print(json.dumps(vscode_config, indent=2, ensure_ascii=False))
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ VSCode配置生成失败: {e}")
        return False

def print_usage_instructions():
    """打印使用说明"""
    print_step(5, "使用说明")
    
    instructions = """
🎯 完整使用流程:

1. 📝 配置VSCode:
   - 打开VSCode设置 (Ctrl+Shift+P → "Preferences: Open Settings (JSON)")
   - 将上面生成的配置添加到settings.json
   - 重启VSCode

2. 🔧 设置端口转发:
   - 在VSCode中按 Ctrl+Shift+P
   - 输入 "Forward a Port"
   - 输入端口号: 8765

3. 🤖 配置AI助手:
   在AI助手的系统提示中添加:
   ```
   # MCP Interactive Feedback Rules
   follow mcp-feedback-enhanced instructions
   ```

4. 🚀 开始使用:
   向AI助手说: "请使用interactive_feedback工具收集我的反馈"

5. 🌐 Web界面访问:
   - 自动打开: http://localhost:8765
   - 或手动访问该地址

🎉 功能特性:
- ✅ 智能反馈工作流
- ✅ 提示管理和自动提交
- ✅ 图片上传 (拖拽或Ctrl+V)
- ✅ 多语言支持 (中文界面)
- ✅ 会话历史和导出
- ✅ Markdown渲染

🔧 快捷键:
- Ctrl+Enter: 提交反馈
- Ctrl+V: 粘贴图片
- Ctrl+I: 聚焦输入框
"""
    
    print(instructions)

def main():
    """主函数"""
    print_header("MCP Feedback Enhanced 最终集成测试")
    
    # 执行所有检查
    checks = []
    
    # 1. 检查安装
    checks.append(check_installation())
    
    # 2. 检查配置
    checks.append(check_configuration())
    
    # 3. 测试MCP服务器
    checks.append(test_mcp_server())
    
    # 4. 生成VSCode配置
    checks.append(generate_vscode_config())
    
    # 5. 显示使用说明
    print_usage_instructions()
    
    # 总结
    print_header("测试结果总结")
    
    if all(checks):
        print("🎉 所有测试通过! MCP Feedback Enhanced 已完全配置成功!")
        print("\n✅ 系统状态:")
        print("   - 环境配置: 完成")
        print("   - 软件安装: 完成") 
        print("   - MCP服务器: 正常")
        print("   - 配置文件: 就绪")
        print("\n🚀 下一步: 重启VSCode并开始使用!")
        return True
    else:
        print("❌ 部分测试失败，请检查上述错误信息")
        failed_steps = [i+1 for i, check in enumerate(checks) if not check]
        print(f"   失败的步骤: {failed_steps}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
