# 🎯 "寸止"命令使用指南

## 🎉 设置完成！

您现在可以使用"寸止"命令来运行Windows .exe文件了！

## 📁 文件存放位置

**您的.exe文件应该放在这个目录：**
```
/root/windows-apps/
```

## 📤 如何传输.exe文件到Linux

### 方法1：使用scp（推荐）
```bash
# 从Windows电脑传输到Linux服务器
scp your_program.exe username@server_ip:/root/windows-apps/

# 示例
scp calculator.exe root@*************:/root/windows-apps/
```

### 方法2：使用SFTP
```bash
# 连接到服务器
sftp username@server_ip

# 切换到目标目录
cd /root/windows-apps

# 上传文件
put your_program.exe

# 退出
quit
```

### 方法3：使用WinSCP（Windows图形界面）
1. 下载并安装WinSCP
2. 连接到您的Linux服务器
3. 导航到 `/root/windows-apps/` 目录
4. 拖拽您的.exe文件到该目录

### 方法4：直接复制（如果有物理访问权限）
```bash
# 如果您有USB或其他存储设备
cp /media/usb/your_program.exe /root/windows-apps/
```

## 🚀 使用"寸止"命令

### 基本用法
```bash
# 显示帮助和可用程序
寸止

# 显示程序列表
寸止 list

# 运行指定程序
寸止 程序名.exe

# 显示帮助
寸止 help
```

### 实际示例
```bash
# 假设您上传了 calculator.exe
寸止 calculator.exe

# 假设您上传了 notepad.exe
寸止 notepad.exe

# 假设您上传了 myapp.exe
寸止 myapp.exe
```

## 📋 命令功能详解

### 1. 查看可用程序
```bash
寸止
```
输出示例：
```
🎯 寸止命令 - 可用的Windows程序：
================================
📁 程序目录: /root/windows-apps

可用程序：
calculator.exe
notepad.exe
myapp.exe

使用方法：
  寸止 程序名.exe    # 运行指定程序
  寸止 list         # 显示程序列表
```

### 2. 查看程序列表（详细信息）
```bash
寸止 list
```
输出示例：
```
📋 Windows程序列表：
-rw-r--r--. 1 <USER> <GROUP> 1024000 Aug 15 10:30 /root/windows-apps/calculator.exe
-rw-r--r--. 1 <USER> <GROUP> 2048000 Aug 15 10:31 /root/windows-apps/notepad.exe
```

### 3. 运行程序
```bash
寸止 calculator.exe
```
输出示例：
```
🚀 启动程序: calculator.exe
📁 路径: /root/windows-apps/calculator.exe
⏰ 启动时间: Thu Aug 15 10:35:22 UTC 2025
================================
[Wine程序运行输出...]
================================
🏁 程序已退出，退出码: 0
```

## 🔧 高级用法

### 传递参数给程序
```bash
# 如果程序支持命令行参数
寸止 myapp.exe --config config.txt
寸止 converter.exe input.txt output.txt
```

### 在后台运行程序
```bash
# 在后台运行（不阻塞终端）
寸止 myapp.exe &

# 或者使用nohup
nohup 寸止 myapp.exe > /dev/null 2>&1 &
```

## 📂 目录结构

```
/root/
├── windows-apps/           # 您的.exe文件存放目录
│   ├── calculator.exe
│   ├── notepad.exe
│   └── myapp.exe
└── .bashrc                # 包含寸止别名配置
```

## 🛠️ 故障排除

### 问题1：命令找不到
```bash
# 如果"寸止"命令不工作，尝试：
source ~/.bashrc

# 或者直接使用：
cunzhi
```

### 问题2：程序无法运行
```bash
# 检查文件是否存在
ls -la /root/windows-apps/

# 检查文件权限
chmod +x /root/windows-apps/your_program.exe

# 查看详细错误信息
寸止 your_program.exe 2>&1 | tee error.log
```

### 问题3：Wine相关问题
```bash
# 配置Wine
winecfg

# 安装必要的运行库
winetricks vcrun2019 corefonts
```

## 📝 完整示例流程

### 1. 准备.exe文件
假设您有一个名为 `myapp.exe` 的Windows程序

### 2. 传输文件
```bash
scp myapp.exe root@your_server:/root/windows-apps/
```

### 3. 验证文件
```bash
寸止 list
```

### 4. 运行程序
```bash
寸止 myapp.exe
```

## 🎊 总结

✅ **"寸止"命令已配置完成**
✅ **程序目录**: `/root/windows-apps/`
✅ **支持所有Wine兼容的Windows程序**
✅ **提供完整的错误处理和帮助信息**

### 快速参考
```bash
寸止                    # 显示帮助
寸止 list              # 显示程序列表
寸止 program.exe       # 运行程序
寸止 help              # 显示详细帮助
```

现在您可以轻松地通过输入"寸止"来管理和运行您的Windows程序了！🚀
