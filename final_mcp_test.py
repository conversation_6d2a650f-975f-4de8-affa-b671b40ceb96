#!/usr/bin/env python3
"""
最终的MCP工具测试脚本
"""

import subprocess
import json
import time
import os

def test_mcp_servers():
    """测试MCP服务器"""
    print("=== 最终MCP工具测试 ===")
    
    # 测试1: 检查全局安装的MCP工具
    print("\n1. 检查全局MCP工具安装:")
    try:
        result = subprocess.run(['npm', 'list', '-g', '--depth=0'], 
                              capture_output=True, text=True, timeout=10)
        if '@modelcontextprotocol' in result.stdout:
            print("✓ MCP工具已全局安装")
            # 显示安装的MCP包
            lines = result.stdout.split('\n')
            for line in lines:
                if '@modelcontextprotocol' in line:
                    print(f"  - {line.strip()}")
        else:
            print("✗ 未找到全局安装的MCP工具")
    except Exception as e:
        print(f"✗ 检查失败: {e}")
    
    # 测试2: 测试文件系统服务器
    print("\n2. 测试文件系统服务器:")
    try:
        # 启动文件系统服务器
        fs_server = subprocess.Popen([
            'npx', '-y', '@modelcontextprotocol/server-filesystem', os.getcwd()
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待一秒让服务器启动
        time.sleep(1)
        
        if fs_server.poll() is None:  # 服务器还在运行
            print("✓ 文件系统服务器启动成功")
            fs_server.terminate()
            fs_server.wait(timeout=5)
        else:
            print("✗ 文件系统服务器启动失败")
            stdout, stderr = fs_server.communicate()
            if stderr:
                print(f"  错误: {stderr.decode()}")
    except Exception as e:
        print(f"✗ 文件系统服务器测试失败: {e}")
    
    # 测试3: 检查配置文件
    print("\n3. 检查MCP配置:")
    config_file = "mcp-config.json"
    if os.path.exists(config_file):
        print(f"✓ 找到配置文件: {config_file}")
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                print(f"  配置的服务器数量: {len(config.get('mcpServers', {}))}")
        except Exception as e:
            print(f"  配置文件读取失败: {e}")
    else:
        print("✗ 未找到MCP配置文件")
    
    # 测试4: 环境检查
    print("\n4. 环境检查:")
    print(f"  Node.js: {get_version('node')}")
    print(f"  npm: {get_version('npm')}")
    print(f"  Python: {get_version('python3')}")
    
    # 测试5: 网络连接
    print("\n5. 网络连接测试:")
    try:
        result = subprocess.run(['curl', '-s', '--connect-timeout', '3', 
                               'https://registry.npmjs.org/'], 
                              capture_output=True, timeout=5)
        if result.returncode == 0:
            print("✓ npm registry连接正常")
        else:
            print("✗ npm registry连接失败")
    except Exception as e:
        print(f"✗ 网络测试失败: {e}")

def get_version(command):
    """获取命令版本"""
    try:
        result = subprocess.run([command, '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            return "未安装"
    except:
        return "检查失败"

def main():
    """主函数"""
    test_mcp_servers()
    
    print("\n=== 总结 ===")
    print("如果看到文件系统服务器启动成功，说明MCP工具基本可用。")
    print("如果有任何问题，请检查:")
    print("1. Node.js和npm版本是否兼容")
    print("2. 网络连接是否正常")
    print("3. 系统权限是否足够")
    print("4. 是否有防火墙阻止连接")

if __name__ == "__main__":
    main()
