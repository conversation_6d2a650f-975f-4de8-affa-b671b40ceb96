#!/bin/bash

# 设置寸止程序的PATH环境变量
# 类似Windows的环境变量设置

echo "🚀 设置寸止程序PATH环境变量..."

# 定义路径
CUNZHI_DIR="/root/windows-apps/cunzhi"
WRAPPER_DIR="/usr/local/bin"

# 检查寸止目录是否存在
if [ ! -d "$CUNZHI_DIR" ]; then
    echo "❌ 寸止目录不存在: $CUNZHI_DIR"
    exit 1
fi

echo "📁 寸止程序目录: $CUNZHI_DIR"
echo "🔍 发现的程序:"
ls -la "$CUNZHI_DIR"/*.exe

echo ""
echo "🔧 创建命令包装器..."

# 为每个.exe文件创建包装器脚本
for exe_file in "$CUNZHI_DIR"/*.exe; do
    if [ -f "$exe_file" ]; then
        # 获取文件名（不含路径和扩展名）
        base_name=$(basename "$exe_file" .exe)
        wrapper_name="$base_name"
        wrapper_path="$WRAPPER_DIR/$wrapper_name"
        
        echo "  创建包装器: $wrapper_name -> $exe_file"
        
        # 创建包装器脚本
        cat > "$wrapper_path" << EOF
#!/bin/bash
# 自动生成的包装器脚本
# 用于运行: $exe_file

# 设置程序路径
PROGRAM_PATH="$exe_file"
PROGRAM_DIR="$CUNZHI_DIR"

# 检查程序是否存在
if [ ! -f "\$PROGRAM_PATH" ]; then
    echo "❌ 程序不存在: \$PROGRAM_PATH"
    exit 1
fi

# 显示启动信息
echo "🚀 启动程序: $base_name"
echo "📁 路径: \$PROGRAM_PATH"
echo "⏰ 时间: \$(date)"
echo "================================"

# 切换到程序目录并运行
cd "\$PROGRAM_DIR"
wine "\$PROGRAM_PATH" "\$@"

# 显示退出状态
EXIT_CODE=\$?
echo ""
echo "================================"
echo "🏁 程序已退出，退出码: \$EXIT_CODE"
EOF

        # 设置执行权限
        chmod +x "$wrapper_path"
        echo "  ✅ 创建成功: $wrapper_path"
    fi
done

echo ""
echo "🎯 创建通用寸止命令..."

# 创建通用的寸止命令（可以列出和运行所有程序）
cat > "$WRAPPER_DIR/寸止" << 'EOF'
#!/bin/bash

# 通用寸止命令
# 可以列出和运行寸止目录中的所有程序

CUNZHI_DIR="/root/windows-apps/cunzhi"

# 如果没有参数，显示可用程序
if [ $# -eq 0 ]; then
    echo "🎯 寸止程序管理器"
    echo "=================="
    echo "📁 程序目录: $CUNZHI_DIR"
    echo ""
    
    if [ -d "$CUNZHI_DIR" ] && [ "$(ls -A $CUNZHI_DIR/*.exe 2>/dev/null)" ]; then
        echo "可用程序："
        for exe in "$CUNZHI_DIR"/*.exe; do
            if [ -f "$exe" ]; then
                base_name=$(basename "$exe" .exe)
                echo "  $base_name"
            fi
        done
        echo ""
        echo "使用方法："
        echo "  寸止 程序名        # 运行指定程序"
        echo "  寸止 list         # 显示详细列表"
        echo "  程序名            # 直接运行程序"
        echo ""
        echo "示例："
        echo "  寸止 寸止"
        echo "  寸止 等一下"
        echo "  寸止              # 或者直接输入程序名"
        echo "  等一下            # 直接调用"
    else
        echo "❌ 没有找到程序文件"
    fi
    exit 0
fi

# 处理特殊命令
case "$1" in
    "list"|"ls"|"列表")
        echo "📋 寸止程序详细列表："
        ls -la "$CUNZHI_DIR"/*.exe 2>/dev/null || echo "没有找到程序文件"
        exit 0
        ;;
    "help"|"帮助"|"-h"|"--help")
        echo "🎯 寸止程序管理器帮助"
        echo "===================="
        echo "用法: 寸止 [程序名]"
        echo ""
        echo "可用程序："
        for exe in "$CUNZHI_DIR"/*.exe; do
            if [ -f "$exe" ]; then
                base_name=$(basename "$exe" .exe)
                echo "  $base_name"
            fi
        done
        exit 0
        ;;
esac

# 运行指定程序
PROGRAM_NAME="$1"
PROGRAM_PATH="$CUNZHI_DIR/$PROGRAM_NAME.exe"

if [ ! -f "$PROGRAM_PATH" ]; then
    echo "❌ 程序不存在: $PROGRAM_NAME"
    echo ""
    echo "可用程序："
    for exe in "$CUNZHI_DIR"/*.exe; do
        if [ -f "$exe" ]; then
            base_name=$(basename "$exe" .exe)
            echo "  $base_name"
        fi
    done
    exit 1
fi

# 运行程序
echo "🚀 启动程序: $PROGRAM_NAME"
echo "📁 路径: $PROGRAM_PATH"
echo "⏰ 时间: $(date)"
echo "================================"

cd "$CUNZHI_DIR"
wine "$PROGRAM_PATH" "${@:2}"

EXIT_CODE=$?
echo ""
echo "================================"
echo "🏁 程序已退出，退出码: $EXIT_CODE"
EOF

chmod +x "$WRAPPER_DIR/寸止"
echo "✅ 创建通用寸止命令: $WRAPPER_DIR/寸止"

echo ""
echo "🎉 设置完成！"
echo "=============="
echo "📁 程序目录: $CUNZHI_DIR"
echo "🔧 包装器目录: $WRAPPER_DIR"
echo ""
echo "现在您可以使用以下命令："
echo ""

# 显示可用的命令
for exe_file in "$CUNZHI_DIR"/*.exe; do
    if [ -f "$exe_file" ]; then
        base_name=$(basename "$exe_file" .exe)
        echo "  $base_name           # 直接运行程序"
    fi
done

echo "  寸止              # 显示程序管理器"
echo "  寸止 程序名        # 通过管理器运行"
echo ""
echo "🎯 就像Windows的环境变量一样，现在这些程序已经加入PATH了！"
