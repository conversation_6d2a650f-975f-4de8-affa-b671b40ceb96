{"name": "@modelcontextprotocol/sdk", "version": "1.17.3", "description": "Model Context Protocol implementation for TypeScript", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/typescript-sdk/issues", "type": "module", "repository": {"type": "git", "url": "git+https://github.com/modelcontextprotocol/typescript-sdk.git"}, "engines": {"node": ">=18"}, "keywords": ["modelcontextprotocol", "mcp"], "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "./client": {"import": "./dist/esm/client/index.js", "require": "./dist/cjs/client/index.js"}, "./server": {"import": "./dist/esm/server/index.js", "require": "./dist/cjs/server/index.js"}, "./*": {"import": "./dist/esm/*", "require": "./dist/cjs/*"}}, "typesVersions": {"*": {"*": ["./dist/esm/*"]}}, "files": ["dist"], "scripts": {"fetch:spec-types": "curl -o spec.types.ts https://raw.githubusercontent.com/modelcontextprotocol/modelcontextprotocol/refs/heads/main/schema/draft/schema.ts", "build": "npm run build:esm && npm run build:cjs", "build:esm": "mkdir -p dist/esm && echo '{\"type\": \"module\"}' > dist/esm/package.json && tsc -p tsconfig.prod.json", "build:esm:w": "npm run build:esm -- -w", "build:cjs": "mkdir -p dist/cjs && echo '{\"type\": \"commonjs\"}' > dist/cjs/package.json && tsc -p tsconfig.cjs.json", "build:cjs:w": "npm run build:cjs -- -w", "examples:simple-server:w": "tsx --watch src/examples/server/simpleStreamableHttp.ts --oauth", "prepack": "npm run build:esm && npm run build:cjs", "lint": "eslint src/", "test": "npm run fetch:spec-types && jest", "start": "npm run server", "server": "tsx watch --clear-screen=false src/cli.ts server", "client": "tsx src/cli.ts client"}, "dependencies": {"ajv": "^6.12.6", "content-type": "^1.0.5", "cors": "^2.8.5", "cross-spawn": "^7.0.5", "eventsource": "^3.0.2", "eventsource-parser": "^3.0.0", "express": "^5.0.1", "express-rate-limit": "^7.5.0", "pkce-challenge": "^5.0.0", "raw-body": "^3.0.0", "zod": "^3.23.8", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.8.0", "@jest-mock/express": "^3.0.0", "@types/content-type": "^1.1.8", "@types/cors": "^2.8.17", "@types/cross-spawn": "^6.0.6", "@types/eslint__js": "^8.42.3", "@types/eventsource": "^1.1.15", "@types/express": "^5.0.0", "@types/jest": "^29.5.12", "@types/node": "^22.0.2", "@types/supertest": "^6.0.2", "@types/ws": "^8.5.12", "eslint": "^9.8.0", "jest": "^29.7.0", "supertest": "^7.0.0", "ts-jest": "^29.2.4", "tsx": "^4.16.5", "typescript": "^5.5.4", "typescript-eslint": "^8.0.0", "ws": "^8.18.0"}, "resolutions": {"strip-ansi": "6.0.1"}}