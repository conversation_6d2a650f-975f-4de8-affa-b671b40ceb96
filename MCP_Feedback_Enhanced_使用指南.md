# MCP Feedback Enhanced 完整使用指南

## 🎉 安装完成状态

✅ **环境准备**: Python 3.11 + uv包管理器  
✅ **软件安装**: MCP Feedback Enhanced v2.6.0  
✅ **配置文件**: VSCode MCP配置已更新  
✅ **网络测试**: SSH Remote环境验证通过  
✅ **服务器测试**: MCP服务器正常启动  

## 📋 配置文件位置

- **MCP配置**: `/root/test/mcp-config.json`
- **VSCode设置**: 需要将配置复制到VSCode的settings.json

## 🔧 VSCode配置步骤

### 1. 打开VSCode设置
在VSCode中按 `Ctrl+Shift+P`，输入 "Preferences: Open Settings (JSON)"

### 2. 添加MCP配置
将以下配置添加到settings.json中：

```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_WEB_HOST": "0.0.0.0",
        "MCP_WEB_PORT": "8765",
        "MCP_DEBUG": "false",
        "MCP_LANGUAGE": "zh-CN"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

### 3. 重启VSCode
完全关闭并重新打开VSCode以加载新配置。

## 🚀 使用方法

### 方法1：通过AI助手调用（推荐）

1. **配置AI助手提示规则**
   在AI助手的系统提示中添加：
   ```
   # MCP Interactive Feedback Rules
   follow mcp-feedback-enhanced instructions
   ```

2. **让AI助手调用工具**
   向AI助手说：
   ```
   请使用interactive_feedback工具收集我的反馈
   ```

3. **Web界面自动打开**
   - AI调用工具后，Web界面会自动在浏览器中打开
   - 地址：`http://localhost:8765`（通过VSCode端口转发）

### 方法2：手动启动Web UI测试

```bash
# 在Linux终端中运行
cd /root/test
MCP_WEB_HOST=0.0.0.0 MCP_WEB_PORT=8765 MCP_LANGUAGE=zh-CN uvx mcp-feedback-enhanced@latest test --web
```

## 🌐 网络访问配置

### SSH Remote环境下的访问方式

#### 选项1：VSCode端口转发（推荐）
1. 在VSCode中按 `Ctrl+Shift+P`
2. 输入 "Forward a Port"
3. 输入端口号：`8765`
4. 在Windows浏览器中访问：`http://localhost:8765`

#### 选项2：直接远程访问
如果服务器有公网IP，可以直接访问：
`http://[服务器IP]:8765`

## 🎯 功能特性

### 智能反馈工作流
- **AI调用** → 自动打开Web界面
- **用户确认/修改** → 实时反馈给AI
- **AI调整行为** → 继续或结束任务

### 丰富的交互功能
- ✅ **提示管理**: 常用提示的CRUD操作
- ✅ **自动提交**: 1-86400秒灵活定时器
- ✅ **会话跟踪**: 本地文件存储，历史导出
- ✅ **图片支持**: 拖拽上传，剪贴板粘贴（Ctrl+V）
- ✅ **多语言**: 中文、英文支持
- ✅ **Markdown渲染**: 丰富的内容显示

### 快捷键支持
- `Ctrl+Enter`：提交反馈
- `Ctrl+V`：粘贴剪贴板图片
- `Ctrl+I`：快速聚焦输入框

## 🔍 故障排除

### 常见问题

#### 1. Web界面无法打开
**解决方案**：
- 检查VSCode端口转发是否设置正确
- 确认端口8765未被其他程序占用
- 尝试刷新浏览器页面

#### 2. MCP工具未显示
**解决方案**：
- 重启VSCode
- 检查MCP配置是否正确
- 查看VSCode输出面板的MCP日志

#### 3. WebSocket连接失败
**解决方案**：
- 直接刷新浏览器页面
- 检查网络连接
- 重新启动MCP服务器

### 调试模式
如果遇到问题，可以启用调试模式：
```json
"env": {
  "MCP_DEBUG": "true"
}
```

## 📊 测试验证

### 验证MCP服务器
```bash
python3.11 test_mcp_server.py
```

### 验证Web UI
```bash
python3.11 test_mcp_web.py
```

## 🎉 成功标志

当看到以下信息时，表示配置成功：

1. **MCP服务器启动**：
   ```
   [SERVER] 🚀 啟動互動式回饋收集 MCP 服務器
   [SERVER] 偵測到 SSH 環境變數: SSH_CONNECTION
   [SERVER] 遠端環境: True
   [SERVER] 介面類型: Web UI
   ```

2. **Web界面访问**：
   - 浏览器能正常打开 `http://localhost:8765`
   - 界面显示中文
   - WebSocket连接正常

3. **AI集成**：
   - AI助手能成功调用 `interactive_feedback` 工具
   - Web界面自动打开
   - 用户反馈能实时传递给AI

## 🚀 下一步

1. **重启VSCode**以加载MCP配置
2. **测试AI助手调用**interactive_feedback工具
3. **体验完整的反馈工作流**

---

🎯 **项目完全实现成功！** 您现在拥有了一个功能完整的AI交互反馈系统，可以在SSH Remote环境中完美运行。
