#!/bin/bash

# 设置"寸止"命令的脚本
# 这个脚本会帮您配置快捷调用Windows程序

echo "🚀 设置'寸止'命令..."

# 1. 创建Windows程序目录
WINDOWS_APPS_DIR="$HOME/windows-apps"
mkdir -p "$WINDOWS_APPS_DIR"
echo "✅ 创建目录: $WINDOWS_APPS_DIR"

# 2. 创建寸止脚本
CUNZHI_SCRIPT="$HOME/.local/bin/寸止"
mkdir -p "$HOME/.local/bin"

cat > "$CUNZHI_SCRIPT" << 'EOF'
#!/bin/bash

# 寸止命令 - 运行您的Windows程序
# 使用方法: 寸止 [程序名]

WINDOWS_APPS_DIR="$HOME/windows-apps"

# 如果没有指定程序名，显示可用程序列表
if [ $# -eq 0 ]; then
    echo "🎯 寸止命令 - 可用的Windows程序："
    echo "================================"
    
    if [ -d "$WINDOWS_APPS_DIR" ] && [ "$(ls -A $WINDOWS_APPS_DIR 2>/dev/null)" ]; then
        echo "📁 程序目录: $WINDOWS_APPS_DIR"
        echo ""
        echo "可用程序："
        for exe in "$WINDOWS_APPS_DIR"/*.exe; do
            if [ -f "$exe" ]; then
                basename "$exe"
            fi
        done
        echo ""
        echo "使用方法："
        echo "  寸止 程序名.exe    # 运行指定程序"
        echo "  寸止 list         # 显示程序列表"
        echo ""
        echo "示例："
        echo "  寸止 notepad.exe"
        echo "  寸止 myapp.exe"
    else
        echo "❌ 程序目录为空或不存在"
        echo "请将您的.exe文件放到: $WINDOWS_APPS_DIR"
        echo ""
        echo "传输文件方法："
        echo "1. 使用scp: scp your_program.exe user@server:$WINDOWS_APPS_DIR/"
        echo "2. 使用sftp上传到该目录"
        echo "3. 直接复制文件到该目录"
    fi
    exit 0
fi

# 特殊命令处理
case "$1" in
    "list"|"ls"|"列表")
        echo "📋 Windows程序列表："
        if [ -d "$WINDOWS_APPS_DIR" ]; then
            ls -la "$WINDOWS_APPS_DIR"/*.exe 2>/dev/null || echo "没有找到.exe文件"
        else
            echo "程序目录不存在: $WINDOWS_APPS_DIR"
        fi
        exit 0
        ;;
    "help"|"帮助"|"-h"|"--help")
        echo "🎯 寸止命令帮助"
        echo "==============="
        echo "用法: 寸止 [选项] [程序名]"
        echo ""
        echo "选项:"
        echo "  无参数        显示可用程序列表"
        echo "  list         显示程序列表"
        echo "  help         显示此帮助信息"
        echo "  程序名.exe    运行指定的Windows程序"
        echo ""
        echo "程序目录: $WINDOWS_APPS_DIR"
        exit 0
        ;;
esac

# 运行指定的程序
PROGRAM_NAME="$1"
PROGRAM_PATH="$WINDOWS_APPS_DIR/$PROGRAM_NAME"

# 检查程序是否存在
if [ ! -f "$PROGRAM_PATH" ]; then
    echo "❌ 程序不存在: $PROGRAM_PATH"
    echo ""
    echo "可用程序："
    for exe in "$WINDOWS_APPS_DIR"/*.exe; do
        if [ -f "$exe" ]; then
            echo "  $(basename "$exe")"
        fi
    done
    exit 1
fi

# 运行程序
echo "🚀 启动程序: $PROGRAM_NAME"
echo "📁 路径: $PROGRAM_PATH"
echo "⏰ 启动时间: $(date)"
echo "================================"

# 切换到程序目录并运行
cd "$WINDOWS_APPS_DIR"
wine "$PROGRAM_PATH" "$@"

# 显示退出状态
EXIT_CODE=$?
echo ""
echo "================================"
echo "🏁 程序已退出，退出码: $EXIT_CODE"
EOF

# 3. 设置执行权限
chmod +x "$CUNZHI_SCRIPT"
echo "✅ 创建寸止脚本: $CUNZHI_SCRIPT"

# 4. 添加到PATH（如果还没有）
if [[ ":$PATH:" != *":$HOME/.local/bin:"* ]]; then
    echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
    echo "✅ 添加 ~/.local/bin 到 PATH"
    echo "⚠️  请运行 'source ~/.bashrc' 或重新登录以生效"
else
    echo "✅ ~/.local/bin 已在 PATH 中"
fi

# 5. 创建别名（备用方案）
if ! grep -q "alias 寸止=" ~/.bashrc; then
    echo "alias 寸止='$CUNZHI_SCRIPT'" >> ~/.bashrc
    echo "✅ 添加寸止别名到 ~/.bashrc"
fi

echo ""
echo "🎉 设置完成！"
echo "================================"
echo "📁 程序目录: $WINDOWS_APPS_DIR"
echo "🔧 寸止脚本: $CUNZHI_SCRIPT"
echo ""
echo "📋 使用方法："
echo "1. 将您的.exe文件放到: $WINDOWS_APPS_DIR"
echo "2. 运行: source ~/.bashrc"
echo "3. 使用: 寸止 程序名.exe"
echo ""
echo "📝 示例："
echo "  寸止              # 显示帮助和程序列表"
echo "  寸止 notepad.exe   # 运行记事本"
echo "  寸止 list          # 显示程序列表"
echo "  寸止 help          # 显示帮助"
