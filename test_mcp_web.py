#!/usr/bin/env python3
"""
测试MCP Feedback Enhanced Web UI
使用正确的SSH Remote配置
"""
import os
import sys
import asyncio
import subprocess
import time

def test_web_ui_with_config():
    """使用正确的配置测试Web UI"""
    print("🧪 测试MCP Feedback Enhanced Web UI (SSH Remote配置)")
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'MCP_WEB_HOST': '0.0.0.0',  # 允许远程访问
        'MCP_WEB_PORT': '8765',     # 使用配置的端口
        'MCP_LANGUAGE': 'zh-CN',    # 中文界面
        'MCP_DEBUG': 'false'        # 关闭调试模式
    })
    
    print("🔧 环境变量设置:")
    print(f"   MCP_WEB_HOST: {env['MCP_WEB_HOST']}")
    print(f"   MCP_WEB_PORT: {env['MCP_WEB_PORT']}")
    print(f"   MCP_LANGUAGE: {env['MCP_LANGUAGE']}")
    
    try:
        # 启动Web UI测试
        print("🚀 启动Web UI服务器...")
        cmd = ['uvx', 'mcp-feedback-enhanced@latest', 'test', '--web']
        
        process = subprocess.Popen(
            cmd,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print("⏳ 等待服务器启动...")
        
        # 实时输出日志
        startup_complete = False
        for line in iter(process.stdout.readline, ''):
            print(line.rstrip())
            
            # 检查服务器是否启动成功
            if "服务器運行在:" in line or "服务器运行在:" in line:
                startup_complete = True
                print("✅ Web UI服务器启动成功!")
                break
            elif "Web 服务器启动成功" in line or "Web 服務器啟動成功" in line:
                startup_complete = True
                
        if startup_complete:
            print("\n🌐 Web UI访问信息:")
            print(f"   本地访问: http://127.0.0.1:8765")
            print(f"   远程访问: http://[您的服务器IP]:8765")
            print("\n💡 在VSCode中设置端口转发:")
            print("   1. 按 Ctrl+Shift+P")
            print("   2. 输入 'Forward a Port'")
            print("   3. 输入端口号: 8765")
            print("   4. 在Windows浏览器中访问: http://localhost:8765")
            
            print("\n🎯 服务器将持续运行，按 Ctrl+C 停止...")
            
            # 保持运行
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 停止服务器...")
                process.terminate()
                process.wait()
        else:
            print("❌ 服务器启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def check_network_access():
    """检查网络访问配置"""
    print("\n🔍 检查网络配置...")
    
    # 检查端口是否被占用
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', 8765))
        sock.close()
        
        if result == 0:
            print("⚠️  端口8765已被占用")
            return False
        else:
            print("✅ 端口8765可用")
            return True
    except Exception as e:
        print(f"❌ 网络检查失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🎯 MCP Feedback Enhanced SSH Remote 测试")
    print("=" * 60)
    
    # 检查网络
    if not check_network_access():
        print("💡 如果端口被占用，请等待几秒钟后重试")
        sys.exit(1)
    
    # 测试Web UI
    success = test_web_ui_with_config()
    
    if success:
        print("\n✅ 测试完成!")
    else:
        print("\n❌ 测试失败!")
        sys.exit(1)
