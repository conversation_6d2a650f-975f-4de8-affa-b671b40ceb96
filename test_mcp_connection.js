#!/usr/bin/env node

/**
 * 测试MCP连接的简单脚本
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('=== MCP连接测试 ===');

// 测试MCP SDK是否可用
try {
    // 尝试导入特定的模块而不是主入口点
    const mcpTypes = require('@modelcontextprotocol/sdk/types');
    console.log('✓ MCP SDK类型模块导入成功');
} catch (error) {
    console.log('✗ MCP SDK导入失败:', error.message);
    console.log('这可能是正常的，让我们继续测试其他功能...');
}

// 测试文件系统服务器
console.log('\n=== 测试文件系统服务器 ===');
const fsServer = spawn('npx', ['-y', '@modelcontextprotocol/server-filesystem', process.cwd()], {
    stdio: ['pipe', 'pipe', 'pipe']
});

let serverOutput = '';
let serverError = '';

fsServer.stdout.on('data', (data) => {
    serverOutput += data.toString();
});

fsServer.stderr.on('data', (data) => {
    serverError += data.toString();
});

// 给服务器一些时间启动
setTimeout(() => {
    if (fsServer.pid) {
        console.log('✓ 文件系统服务器启动成功 (PID:', fsServer.pid, ')');
        fsServer.kill();
    } else {
        console.log('✗ 文件系统服务器启动失败');
        if (serverError) {
            console.log('错误信息:', serverError);
        }
    }
    
    console.log('\n=== 测试完成 ===');
    console.log('如果看到成功消息，说明MCP工具已经可以正常工作了！');
}, 2000);

fsServer.on('error', (error) => {
    console.log('✗ 服务器启动错误:', error.message);
});
