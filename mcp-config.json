{"mcpServers": {"mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "env": {"MCP_WEB_HOST": "0.0.0.0", "MCP_WEB_PORT": "8765", "MCP_DEBUG": "false", "MCP_LANGUAGE": "zh-CN"}, "autoApprove": ["interactive_feedback"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/root/test"], "env": {}}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": ""}}}}